#include <errno.h>
#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include "mycan.h"

static const unsigned char dlc2len[] = {0, 1, 2, 3, 4, 5, 6, 7,
                                        8, 12, 16, 20, 24, 32, 48, 64};

/* get data length from raw data length code (DLC) */
unsigned char can_fd_dlc2len(unsigned char dlc)
{
    return dlc2len[dlc & 0x0F];
}

static const unsigned char len2dlc[] = {0, 1, 2, 3, 4, 5, 6, 7, 8,       /* 0 - 8 */
                                        8, 8, 8, 8,                      /* 9 - 12 */
                                        9, 9, 9, 9,                      /* 13 - 16 */
                                        10, 10, 10, 10,                  /* 17 - 20 */
                                        11, 11, 11, 11,                  /* 21 - 24 */
                                        12, 12, 12, 12, 12, 12, 12, 12,  /* 25 - 32 */
                                        13, 13, 13, 13, 13, 13, 13, 13,  /* 33 - 40 */
                                        13, 13, 13, 13, 13, 13, 13, 13,  /* 41 - 48 */
                                        14, 14, 14, 14, 14, 14, 14, 14,  /* 49 - 56 */
                                        14, 14, 14, 14, 14, 14, 14, 14}; /* 57 - 63 */

/* map the sanitized data length to an appropriate data length code */
unsigned char can_fd_len2dlc(unsigned char len)
{
    if (len > 64)
        return 0xF;

    return len2dlc[len];
}

/*
 * @description        : 字符串按格式输出
 * @param - *destpoint : 字符串格式缓冲区
 * @param - *fmt       : 多个参数
 * @return		       : 按格式输出字符串缓冲区首指针
 */
char func_dprintf1(char *destpoint, char *fmt, ...)
{
    va_list arg_ptr;
    char ulen, *tmpBuf;

    tmpBuf = destpoint;
    va_start(arg_ptr, fmt);
    ulen = vsprintf(tmpBuf, fmt, arg_ptr);

    va_end(arg_ptr);
    return ulen;
}

/*
 * @description     : 打开canfd外设，设置波特率，创建文件描述符
 * @param - *device : 设备名称
 * @param - para    : canfd应用参数
 * @return		    : 打开设备执成功返回文件描述符，失败返回-1
 */
int func_open_canfd(char *device, struct_can_param para)
{
    FILE *fstream = NULL;
    char buff[300] = {0}, command[50] = {0};
    int fd = -1;
    struct sockaddr_can addr;
    struct ifreq ifr;

    /*关闭can设备 ifconfig can0 down*/
    memset(buff, 0, sizeof(buff));
    memset(command, 0, sizeof(command));
    func_dprintf1(command, "ifconfig %s down", device);
    printf("%s \n", command);

    if (NULL == (fstream = popen(command, "w")))
    {
        fprintf(stderr, "execute command failed: %s", strerror(errno));
    }
    while (NULL != fgets(buff, sizeof(buff), fstream))
    {
        printf("%s\n", buff);
        if (strstr(buff, "No such device") || strstr(buff, "Cannot find device"))
            return -1;
    }
    sleep(1);

    /*设置can波特率 ip link set can0 up type can bitrate 250000 dbitrate 4000000 fd on*/
    memset(buff, 0, sizeof(buff));
    memset(command, 0, sizeof(command));
    if (para.loopback_mode == 1)
    {
        func_dprintf1(command, "ip link set %s up type can bitrate %d dbitrate %d fd on loopback on", device, para.baudrate, para.data_baudrate);
    }
    else
    {
        func_dprintf1(command, "ip link set %s up type can bitrate %d dbitrate %d fd on loopback off", device, para.baudrate, para.data_baudrate);
    }
    printf("%s \n", command);
    if (NULL == (fstream = popen(command, "w")))
    {
        fprintf(stderr, "execute command failed: %s", strerror(errno));
    }
    while (NULL != fgets(buff, sizeof(buff), fstream))
    {
        printf("%s\n", buff);
        if (strstr(buff, "No such device") || strstr(buff, "Cannot find device"))
            return -1;
    }
    sleep(1);

    /*打开can设备 ifconfig can0 up*/
    memset(buff, 0, sizeof(buff));
    memset(command, 0, sizeof(command));
    func_dprintf1(command, "ifconfig %s up", device);
    printf("%s \n", command);

    if (NULL == (fstream = popen(command, "w")))
    {
        fprintf(stderr, "execute command failed: %s", strerror(errno));
    }
    while (NULL != fgets(buff, sizeof(buff), fstream))
    {
        printf("%s\n", buff);
        if (strstr(buff, "No such device") || strstr(buff, "Cannot find device"))
            return -1;
    }
    sleep(3);

    /* 创建 socket */
    fd = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (fd < 0)
    {
        printf("socket:%s", strerror(errno));
        return -1;
    }

    /* 设置接口设备名称 */
    strcpy(ifr.ifr_name, device);
    /* 确定接口 index */
    ifr.ifr_ifindex = if_nametoindex(ifr.ifr_name);
    if (!ifr.ifr_ifindex)
    {
        printf("if_nametoindex:%s\n", strerror(errno));
        return -1;
    }

    memset(&addr, 0, sizeof(addr));
    addr.can_family = AF_CAN;
    addr.can_ifindex = ifr.ifr_ifindex;

    /* 绑定 socket到 CAN 接口 */
    if (bind(fd, (struct sockaddr *)&addr, sizeof(addr)) < 0)
    {
        printf("bind:%s\n", strerror(errno));
        return -1;
    }

    return fd;
}

/*
 * @description : 设置canfd模式，回环模式和过滤规则
 * @param - fd  : 文件描述符
 * @param - para: canfd应用参数
 * @return		: 设置成功返回1，失败返回-1
 */
int func_set_canfd(int fd, struct_can_param para)
{
    int enable_canfd = 1;
    // int loopback = 1;
    // int reciveown = 1;

    //设置FD模式
    if (setsockopt(fd, SOL_CAN_RAW, CAN_RAW_FD_FRAMES, &enable_canfd, sizeof(enable_canfd)) < 0)
    {
        printf("error when enabling CAN FD support\n");
        return -1;
    }
    if (para.loopback_mode == 1)
    {
        //回环设置 0 关闭回环  1 打开回环
        //setsockopt(fd, SOL_CAN_RAW, CAN_RAW_LOOPBACK, &loopback, sizeof(loopback));
        //接收自己的帧 0 不接收自己帧 1 接收自己帧
        //setsockopt(fd, SOL_CAN_RAW, CAN_RAW_RECV_OWN_MSGS, &reciveown, sizeof(reciveown));
    }
    /* 设置过滤规则 */
    if (setsockopt(fd, SOL_CAN_RAW, CAN_RAW_FILTER, &para.filter, sizeof(para.filter)) < 0)
    {
        printf("error when set filter\n");
        return -1;
    }
    return 1;
}

/*
 * @description    : canfd接收一个canfd帧
 * @param - fd     : 文件描述符
 * @param - *pframe: 一个canfd帧结构体指针
 * @return		   : 接收数据长度
 */
int func_receive_canfd_frame(int fd, struct canfd_frame *pframe)
{
    int rx_count = 0;

    rx_count = recv(fd, pframe, sizeof(*pframe), 0);
    if (rx_count <= 0)
    {
        return rx_count;
    }
    else
    {
        if (pframe->can_id & CAN_EFF_FLAG) /*如果是扩展帧，清除扩展帧标识*/
        {
            pframe->can_id &= (~CAN_EFF_FLAG);
        }
        else
        {
            pframe->can_id &= (~CAN_SFF_MASK);
        }
    }

    return pframe->len;
}

/*
 * @description  : canfd接收一组数据包
 * @param - fd   : 文件描述符
 * @param - *buff: 要接收canfd一组数据的缓冲区首指针
 * @param - len  : 接收到一组数据的长度
 * @return		 : 接收数据长度
 */
int func_receive_canfd_buff(int fd, unsigned char *buff, int len)
{
    int receive_len = 0, total_receive_len = 0;
    struct canfd_frame frame;
    int i = 0;

    while (1)
    {
        receive_len = func_receive_canfd_frame(fd, &frame);
        for (i = 0; i < receive_len; i++)
        {
            *(buff + total_receive_len) = frame.data[i];
            total_receive_len++;
        }
        if ((receive_len <= 0) || (total_receive_len > (len - 64)))
        {
            return total_receive_len;
        }
    }

    return total_receive_len;
}

/*
 * @description    : canfd发送一个canfd帧
 * @param - fd     : 文件描述符
 * @param - *pframe: 一个canfd帧结构体指针
 * @param - param  : canfd应用参数
 * @return		   : 发送数据长度，发送失败返回-1
 */
int func_send_canfd_frame(int fd, struct canfd_frame *pframe, struct_can_param param)
{
    int result = 0;

    if (param.extend == 1) /*扩展帧增加扩展帧标志*/
    {
        pframe->can_id &= CAN_EFF_MASK;
        pframe->can_id |= CAN_EFF_FLAG;
    }
    else
    {
        pframe->can_id &= CAN_SFF_MASK;
    }
	pframe->flags |= 0x01; 
    result = send(fd, pframe, sizeof(struct canfd_frame), 0);
    if (result == -1)
    {
        printf("send:%s\n", strerror(errno));
        return -1;
    }

    return result;
}
/*注意：canfd的数据虽然能发送0-64个数据，但是数据长度仅能是0, 1, 2, 3, 4, 5, 6, 7, 8, 12, 16, 20, 24, 32, 48, 64
  这几个档，例如，若想发送9个数据，实际发送的是12个数据，后3个数据补0，回环测试，接收也是12个数。为解决收发不对应的问题有如下2种方案
  1.占用ID的某7位，附上实际发送长度。接收端根据实际长度处理接收的数据，
  2.将发送的数据拆分，例9个数，先发8个数据，再发1个数据，这样接收端无需特殊处理，下述程序按照方案2实现
  当然，如果实际应用规约中有数据长度或起始帧，结束帧，也可以不用在应用上特殊处理分帧，因为补0肯定会在帧末尾，通过规约解析可以排除掉末尾0数据*/
/*
 * @description  : canfd发送一组数据包
 * @param - fd   : 文件描述符
 * @param - *buff: 要发送canfd一组数据的缓冲区首指针
 * @param - len  : 发送数据的长度
 * @param - param: canfd应用参数
 * @return		 : 实际发送数据长度
 */
int func_send_canfd_buff(int fd, unsigned char *buff, int len, struct_can_param param)
{
    int remain_frame_len = 0, send_frame_len = 0;
    struct canfd_frame frame;
    int i = 0;

    remain_frame_len = len;
    while (1)
    {
        memset(&frame, 0, sizeof(struct canfd_frame));
        if (remain_frame_len >= 64)
        {
            frame.len = 64;
            remain_frame_len -= 64; //总发送数据-每帧发送数量
        }
        else
        {
            /* ensure discrete CAN FD length values 0..8, 12, 16, 20, 24, 32, 64 */
            frame.len = can_fd_dlc2len(can_fd_len2dlc(remain_frame_len));
            remain_frame_len -= frame.len; //
        }
        //printf("frame_len=%d,remain_frame_len=%d,send_frame_len=%d\n", frame.len, remain_frame_len, send_frame_len);

        frame.can_id = param.id;
        for (i = 0; i < frame.len; i++)
        {
            frame.data[i] = buff[send_frame_len + i];
        }
        send_frame_len += frame.len;
        func_send_canfd_frame(fd, &frame, param);
        if (remain_frame_len == 0)
        {
            return len;
        }
    }
    return len;
}