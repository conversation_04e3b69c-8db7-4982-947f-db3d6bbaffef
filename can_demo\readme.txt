demo name : can_demo
demo ver : sv01.001
first edition compilation time :2022/01/11

note:

1)different platforms have to choose different cross-compilation .you need to modify makefile redefine "CC".

2)can2.0 bitrate Kbps default:250K,range :5，10，20，40，50，80，100，125，200，250，400，500，666，800，1000

3)canfd bitrate Kbps 5k to 5M ,default:250K,range :5，10，20，40，50，80，100，125，200，250，400，500，666，800，1000,2000,4000,5000
        dbitrate Kbps 250k to 5M,default:250K,range :250，500，1000,2000,4000,5000
        dbitrate must greater than bitrate
        recommend dbitrate is 1-8 times bitrate.

4)about canbus ID filter and mask
    If a mask bit is set to a zero, the corresponding ID bit will automatically be accepted, regardless of the value of the filter bit.

    If a mask bit is set to a one, the corresponding ID bit will be compare with the value of the filter bit;
    if they match it is accepted otherwise the frame is rejected. 

    A filter matches, when received_can_id & mask == can_id & mask -- Mask=1 : Do Care Bits

    Example 1. we wish to accept only frames with ID of 00001567 (hexadecimal values)
    set filter to 00001567
    set mask to 1FFFFFFF --- Every Bit must match filter
    when a frame arrives its ID is compared with the filter and all bits must match; any frame that does not match ID 00001567 is rejected 

    Example 2. we wish to accept only frames with IDs of 00001560 thru to 0000156F
    set filter to 00001560
    set mask to 1FFFFFF0 Low 4 Bits dont care
    when a frame arrives its ID is compared with the filter and all bits except bits 0 to 3 must match; any frame other frame is rejected 

    Example 3. we wish to accept only frames with IDs of 00001560 thru to 00001567
    set filter to 00001560
    set mask to 1FFFFFF8 Low 3 Bits dont care
    when a frame arrives its ID is compared with the filter and all bits except bits 0 to 2 must match; any frame other frame is rejected 

    Example 4. we wish to accept any frame
    set filter to 0
    set mask to 0 --- Every Bits dont care
    all frames are accepted 
    In practice Canbus interfaces tends to have a number of filters and masks so combinations of IDs can be accepted,

    e.g. a module that carries out a number of different tasks.// mask bit n | filter bit n | message ID bit n | result  

   //     Mask       Filter         ID
   //     0             x               x             accept 
   //     1             0               0             accept 
   //     1             0               1             reject 
   //     1             1               0             reject 
   //     1             1               1             accept
   
   
   禁止高 7 位都为隐性，禁止设定： ID=1111111XXXX

some more examples:
