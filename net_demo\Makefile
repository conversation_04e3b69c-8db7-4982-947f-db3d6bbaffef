
COMMIT_HASH := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
REPO_NAME := $(shell basename `git rev-parse --show-toplevel 2>/dev/null` 2>/dev/null || echo "unknown")
CFLAGS += -DCOMMIT_HASH=\"$(COMMIT_HASH)\" -DREPO_NAME=\"$(REPO_NAME)\"

net_demo:main.o tcp_client.o tcp_server.o udp_client.o udp_server.o
	$(CC)	-Wall	main.o tcp_client.o tcp_server.o udp_client.o udp_server.o	-o	net_demo -lpthread
main.o:main.c net.h
	$(CC)	-c	-Wall	main.c	-o	main.o -lpthread
tcp_client.o:tcp_client.c
	$(CC)	-c	-Wall	tcp_client.c	-o	tcp_client.o
tcp_server.o:tcp_server.c
	$(CC)	-c	-Wall	tcp_server.c	-o	tcp_server.o
udp_client.o:udp_client.c
	$(CC)	-c	-Wall	udp_client.c	-o	udp_client.o
udp_server.o:udp_server.c
	$(CC)	-c	-Wall	udp_server.c	-o	udp_server.o
clean:
	$(RM) *.o	net_demo

