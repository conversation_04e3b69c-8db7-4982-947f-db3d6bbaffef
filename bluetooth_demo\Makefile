
COMMIT_HASH := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
REPO_NAME := $(shell basename `git rev-parse --show-toplevel 2>/dev/null` 2>/dev/null || echo "unknown")
CFLAGS += -DCOMMIT_HASH=\"$(COMMIT_HASH)\" -DREPO_NAME=\"$(REPO_NAME)\"


bluetooth_demo:main.o bt_hci.o bt_l2cap.o bt_rfcomm.o 
	$(CC)	-Wall	main.o bt_hci.o bt_l2cap.o bt_rfcomm.o	-o	bluetooth_demo -lpthread -lbluetooth
main.o:main.c bt.h bt_hci.h
	$(CC)	-c	-Wall	main.c	-o	main.o -lpthread
bt_hci.o:bt_hci.c
	$(CC)	-c	-Wall	bt_hci.c	-o	bt_hci.o -lbluetooth
bt_l2cap.o:bt_l2cap.c
	$(CC)	-c	-Wall	bt_l2cap.c	-o	bt_l2cap.o -lbluetooth
bt_rfcomm.o:bt_rfcomm.c
	$(CC)	-c	-Wall	bt_rfcomm.c	-o	bt_rfcomm.o -lbluetooth
clean:
	$(RM) *.o	bluetooth_demo



