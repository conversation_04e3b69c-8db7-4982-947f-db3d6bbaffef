
COMMIT_HASH := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
REPO_NAME := $(shell basename `git rev-parse --show-toplevel 2>/dev/null` 2>/dev/null || echo "unknown")
CFLAGS += -DCOMMIT_HASH=\"$(COMMIT_HASH)\" -DREPO_NAME=\"$(REPO_NAME)\"

watchdog:watchdog.o
	$(CC)	-Wall	watchdog.o -o	watchdog
watchdog.o:watchdog.c
	$(CC)	-c	-Wall	watchdog.c	-o	watchdog.o
clean:
	$(RM) *.o	watchdog


