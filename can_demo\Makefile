
COMMIT_HASH := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
REPO_NAME := $(shell basename `git rev-parse --show-toplevel 2>/dev/null` 2>/dev/null || echo "unknown")
CFLAGS += -DCOMMIT_HASH=\"$(COMMIT_HASH)\" -DREPO_NAME=\"$(REPO_NAME)\"

can_demo:main.o can.o canfd.o
	$(CC)	-Wall	main.o can.o canfd.o -o	can_demo
main.o:main.c mycan.h 
	$(CC)	-c	-Wall	main.c	-o	main.o
can.o:can.c mycan.h
	$(CC)	-c	-Wall	can.c	-o	can.o
canfd.o:canfd.c mycan.h
	$(CC)	-c	-Wall	canfd.c	-o	canfd.o
clean:
	$(RM) *.o	can_demo

