#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <pthread.h>
#include <bluetooth/bluetooth.h>
#include "bt.h"
#include "bt_hci.h"

int sockfd;   // 链接的fd
int listenfd; // 服务端监听的fd

bdaddr_t local_addr; // 设备本地模块地址

pthread_t net_thread; // 管理链接的线程

char ver[20] = {"ver01.001"}; // 版本
#ifndef COMMIT_HASH
#define COMMIT_HASH "unknown"
#endif

char commit[64] = {COMMIT_HASH};

#ifndef REPO_NAME
#define REPO_NAME "unknown"
#endif

char repo_name[64] = {REPO_NAME};

// 要连接的远端蓝牙设备名称，设备地址，设备名称长度
char remote_bt_name[50] = {0};
char remote_bt_addr[19] = {0};
int remote_bt_name_len = 0;

// 要建立链接端口，初始模式
int listen_port = 1;
char run_mode = RFCOMM_SERVER;
unsigned int connect_state = DISCONNECT;
unsigned int establish_listen = CREATE_FALSE;

// 数据收发缓冲区，及收发数据长度
char send_buff[500], receive_buff[500];
int send_num = 0, receive_num = 0;

// 数据收发应用
char active_send_buff[500] = "test bluetooth";
int active_send_mode = 0, active_send_num = 14, active_send_time = 1000, active_send_time_count = 0;
int real_send_num = 0;
int loopback_send_mode = 0;

/*
 * @description : 自定义打印函数
 * @param - buff:  打印数据缓冲区
 * @param - lens:  打印数据长度
 * @param - mode:  打印格式
 * @return		: 无
 */
void func_my_print(char *buff, unsigned int lens, unsigned char mode)
{
    int i = 0;

    switch (mode)
    {
    case 'H': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("0X%02X  ", buff[i]);
        }
        break;
    case 'h': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("0x%02x  ", buff[i]);
        }
        break;
    case 'd': // 按照10进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%02d  ", buff[i]);
        }
        break;
    case 'c': // 按照字符打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%c", buff[i]);
        }
        break;
    default:
        break;
    }
    printf("\n");
}

/*
 * @description  : 打印参数设置格式
 * @param - pname: 函数名
 * @return		 : 无
 */
static void print_usage(const char *pname)
{
    printf("Usage: %s [-r] [-l] [-s listen_port] [-c name port] [-t data time] [-L] [-v] "
           "\n\t'-r' for use RFCOMM mode"
           "\n\t'-l' for use L2CAP mode"
           "\n\t'-s listen_port' for server mode and listen port (RFCOMM mode from 1 to 30)(L2CAP mode odd from 4097 to 32765)"
           "\n\t'-c name port' for client mode , name is remote buletooth name , port is remote buletooth listen port"
           "\n\t'-t data time' for interval set time actively sends the data,unit is s"
           "\n\t'-L' data loopback,send the received data"
           "\n\t'-v' show version"
           "\n\texample :  L2CAP client  --> ./bluetooth_demo -l -c RTK 4097 -t aabbcddee 2"
           "\n\texample :  L2CAP server  --> ./bluetooth_demo -l -s 4097 -L"
           "\n\texample :  RFCOMM client --> ./bluetooth_demo -r -c RTK 1 -t aabbcddee 2"
           "\n\texample :  RFCOMM server --> ./bluetooth_demo -r -s 1 -L\n ",
           pname);
}

/*
 * @description : 解析函数带入参数
 * @param - numb: 参数个数
 * @param - *param: 带入参数数组指针
 * @param - *net: 网络应用参数
 * @return		: 无
 */
void get_param(int numb, char *param[])
{
    int i = 0, port = 0, len = 0;
    unsigned int l2_mode = 0, server = 0;

    if (numb <= 2)
        return;

    for (i = 1; i < numb; i++)
    {
        if (!strcmp(param[i], "-r"))
        {
            l2_mode = 0;
            continue;
        }
        if (!strcmp(param[i], "-l"))
        {
            l2_mode = 1;
            continue;
        }
        if (!strcmp(param[i], "-s"))
        {
            server = 1;

            i++;
            port = atoi(param[i]);
            if (port > 0)
            {
                listen_port = port;
                printf("listen port = %d\n", listen_port);
            }
            continue;
        }
        if (!strcmp(param[i], "-c"))
        {
            server = 0;

            i++;
            len = strlen(param[i]);
            if (len > 0)
            {
                remote_bt_name_len = len;
                memcpy(remote_bt_name, param[i], len); // 要连接服务端的蓝牙名称
            }

            i++;
            port = atoi(param[i]); // 要连接服务器的监听端口
            if (port > 0)
            {
                listen_port = port;
                printf("port = %d\n", listen_port);
            }
            continue;
        }
        if (!strcmp(param[i], "-t"))
        {
            active_send_mode = 1;

            i++;
            len = strlen(param[i]);
            if (len > 0)
            {
                active_send_num = len;
                memcpy(active_send_buff, param[i], len); // 要发送的字符串

                i++;
                len = atoi(param[i]); // 发送间隔
                if (len > 0)
                {
                    active_send_time = len * 1000; // 转换为ms单位
                    active_send_time_count = active_send_time;
                }
            }
            continue;
        }
        if (!strcmp(param[i], "-L"))
        {
            loopback_send_mode = 1;
            continue;
        }
        if (!strcmp(param[i], "-v"))
        {
            printf("bluetooth_demo ver: %s, repo: %s, commit: %s\n", ver, repo_name, commit);
            continue;
        }
    }

    if (l2_mode == 1)
    {
        if (server == 1)
            run_mode = L2CAP_SERVER;
        else
            run_mode = L2CAP_CLIENT;
        if (listen_port < 4097) // L2CAP 模式 端口是4097~32765之间的奇数
            listen_port = 4097;
    }
    else
    {
        if (server == 1)
            run_mode = RFCOMM_SERVER;
        else
            run_mode = RFCOMM_CLIENT;
    }
}

/*
 * @description : 蓝牙网络连接管理线程
 * @param - arg : 参数
 * @return		: 无
 */
void *net_manage(void *arg)
{
    int result = 0;

    while (1)
    {
        switch (run_mode)
        {
        case RFCOMM_CLIENT:
            if (connect_state == DISCONNECT)
            {
                // 蓝牙做客户端，搜索附近蓝牙
                result = get_addr_from_name(remote_bt_name, remote_bt_name_len, remote_bt_addr);
                if (result < 0)
                {
                    break;
                }
                // 蓝牙做客户端，主动跟服务端建立链接
                result = func_bt_rf_client_connect(&sockfd, local_addr, remote_bt_addr, listen_port);
                if (result >= 0)
                {
                    connect_state = CONNECT;
                    printf("buletooth connected\n");
                }
            }
            else
            {
                // 检测TCP链接状态
                result = func_detect_bt_rf_link(sockfd);
                if (result < 0)
                {
                    connect_state = DISCONNECT;
                    close(sockfd);
                    printf("ReadCwnd judge connect braek\n");
                }
            }
            break;
        case RFCOMM_SERVER:
            if (establish_listen == CREATE_FALSE)
            {
                // 蓝牙做服务端，建立监听
                result = func_create_bt_rf_server_listen(&listenfd, listen_port, local_addr);
                if (result >= 0)
                {
                    establish_listen = CREATE_TURE;
                    printf("bluetooth create server listen ok\n");
                }
                else
                {
                    if (listenfd > 0)
                    {
                        close(listenfd);
                        listenfd = 0;
                    }
                }
            }
            else
            {
                // 接受客户端建立链接
                result = func_bt_rf_server_accept(listenfd, &sockfd);
                if (result >= 0)
                {
                    connect_state = CONNECT;
                    printf("bluetooth connected\n");
                }

                // 检测bluetooth链接状态
                if (connect_state == CONNECT)
                {
                    result = func_detect_bt_rf_link(sockfd);
                    if (result < 0)
                    {
                        connect_state = DISCONNECT;
                        close(sockfd);
                        sockfd = 0;
                        printf("ReadCwnd judge connect braek\n");
                    }
                }
            }
            break;
        case L2CAP_CLIENT:
            if (connect_state == DISCONNECT)
            {
                // 蓝牙做客户端，搜索附近蓝牙
                result = get_addr_from_name(remote_bt_name, remote_bt_name_len, remote_bt_addr);
                if (result < 0)
                {
                    break;
                }
                // 蓝牙做客户端，主动跟服务端建立链接
                result = func_bt_l2_client_connect(&sockfd, local_addr, remote_bt_addr, listen_port);
                if (result >= 0)
                {
                    connect_state = CONNECT;
                    printf("buletooth connected\n");
                }
            }
            else
            {
                // 检测TCP链接状态
                result = func_detect_bt_l2_link(sockfd);
                if (result < 0)
                {
                    connect_state = DISCONNECT;
                    close(sockfd);
                    printf("ReadCwnd judge connect braek\n");
                }
            }
            break;
        case L2CAP_SERVER:
            if (establish_listen == CREATE_FALSE)
            {
                // 蓝牙做服务端，建立监听
                result = func_create_bt_l2_server_listen(&listenfd, listen_port, local_addr);
                if (result >= 0)
                {
                    establish_listen = CREATE_TURE;
                    printf("bluetooth create l2cap server listen ok\n");
                }
                else
                {
                    if (listenfd > 0)
                    {
                        close(listenfd);
                        listenfd = 0;
                    }
                }
            }
            else
            {
                // 接受客户端建立链接
                result = func_bt_l2_server_accept(listenfd, &sockfd);
                if (result >= 0)
                {
                    connect_state = CONNECT;
                    printf("bluetooth connected\n");
                }
                // 检测bluetooth链接状态
                if (connect_state == CONNECT)
                {
                    result = func_detect_bt_l2_link(sockfd);
                    if (result < 0)
                    {
                        connect_state = DISCONNECT;
                        close(sockfd);
                        sockfd = 0;
                        printf("ReadCwnd judge connect braek\n");
                    }
                }
            }
            break;
        }
        usleep(50000);
    }
}

/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    printf("Copyright 2025 Forlinx Embedded Technology Co., Ltd.\n");
    printf("bluetooth_demo ver: %s, repo: %s, commit: %s\n", ver, repo_name, commit);
    // 检测是否有参数
    if (argc < 2)
    {
        print_usage(argv[0]);
        exit(1);
    }
    // 检测是否有--h或--help
    if ((!strcmp(argv[1], "--h")) || (!strcmp(argv[1], "--help")))
    {
        print_usage(argv[0]);
        exit(1);
    }
    // 获取通信参数
    get_param(argc, argv);

    // 打开本地设备
    if (func_bt_open(&local_addr) < 0)
    {
        printf("open bluetooth err\n");
        exit(1);
    }

    // 创建链接管理线程
    if (pthread_create(&net_thread, NULL, net_manage, (void *)NULL))
    {
        printf("Pthread net_manage create error\n");
        exit(1);
    }

    while (1)
    {
        if (connect_state == DISCONNECT)
            continue;

        switch (run_mode)
        {
        case RFCOMM_SERVER:
            // 数据接收
            receive_num = func_bt_rf_receive(sockfd, receive_buff, sizeof(receive_buff));
            if (receive_num > 0)
            {
                printf("[nread=%d ] ", receive_num);
                func_my_print(receive_buff, receive_num, 'c');
            }
            // 组织发送数据
            if ((1 == loopback_send_mode) && (receive_num > 0)) // 数据回环处理
            {
                send_num = receive_num;
                memcpy(send_buff, receive_buff, receive_num);
            }
            else if (1 == active_send_mode)
            {
                if (active_send_time_count >= active_send_time)
                {
                    active_send_time_count = 0;
                    send_num = active_send_num;
                    memcpy(send_buff, active_send_buff, active_send_num);
                }
                else
                {
                    active_send_time_count++;
                }
            }
            // 数据发送
            if (send_num > 0)
            {
                real_send_num = func_bt_rf_send(sockfd, send_buff, send_num);
                if (real_send_num > 0)
                {
                    printf("[nwrite=%d] ", real_send_num);
                    func_my_print(send_buff, real_send_num, 'c');
                }
                else
                {
                    close(sockfd);
                    connect_state = DISCONNECT;
                }
                memset(send_buff, 0, send_num);
                send_num = 0;
            }
            break;
        case RFCOMM_CLIENT:
            // 数据接收
            receive_num = func_bt_rf_receive(sockfd, receive_buff, sizeof(receive_buff));
            if (receive_num > 0)
            {
                printf("[nread=%d ] ", receive_num);
                func_my_print(receive_buff, receive_num, 'c');
            }
            // 组织发送数据
            if ((1 == loopback_send_mode) && (receive_num > 0)) // 数据回环处理
            {
                send_num = receive_num;
                memcpy(send_buff, receive_buff, receive_num);
            }
            else if (1 == active_send_mode)
            {
                if (active_send_time_count >= active_send_time)
                {
                    active_send_time_count = 0;
                    send_num = active_send_num;
                    memcpy(send_buff, active_send_buff, active_send_num);
                }
                else
                {
                    active_send_time_count++;
                }
            }
            // 数据发送
            if (send_num > 0)
            {
                real_send_num = func_bt_rf_send(sockfd, send_buff, send_num);
                if (real_send_num > 0)
                {
                    printf("[nwrite=%d] ", real_send_num);
                    func_my_print(send_buff, real_send_num, 'c');
                }
                else
                {
                    close(sockfd);
                    connect_state = DISCONNECT;
                }
                memset(send_buff, 0, send_num);
                send_num = 0;
            }
            break;
        case L2CAP_SERVER:
            // 数据接收
            receive_num = func_bt_l2_receive(sockfd, receive_buff, sizeof(receive_buff));
            if (receive_num > 0)
            {
                printf("[nread=%d ] ", receive_num);
                func_my_print(receive_buff, receive_num, 'c');
            }
            // 组织发送数据
            if ((1 == loopback_send_mode) && (receive_num > 0)) // 数据回环处理
            {
                send_num = receive_num;
                memcpy(send_buff, receive_buff, receive_num);
            }
            else if (1 == active_send_mode)
            {
                if (active_send_time_count >= active_send_time)
                {
                    active_send_time_count = 0;
                    send_num = active_send_num;
                    memcpy(send_buff, active_send_buff, active_send_num);
                }
                else
                {
                    active_send_time_count++;
                }
            }
            // 数据发送
            if (send_num > 0)
            {
                real_send_num = func_bt_l2_send(sockfd, send_buff, send_num);
                if (real_send_num > 0)
                {
                    printf("[nwrite=%d] ", real_send_num);
                    func_my_print(send_buff, real_send_num, 'c');
                }
                else
                {
                    close(sockfd);
                    connect_state = DISCONNECT;
                }
                memset(send_buff, 0, send_num);
                send_num = 0;
            }
            break;
        case L2CAP_CLIENT:
            // 数据接收
            receive_num = func_bt_l2_receive(sockfd, receive_buff, sizeof(receive_buff));
            if (receive_num > 0)
            {
                printf("[nread=%d ] ", receive_num);
                func_my_print(receive_buff, receive_num, 'c');
            }
            // 组织发送数据
            if ((1 == loopback_send_mode) && (receive_num > 0)) // 数据回环处理
            {
                send_num = receive_num;
                memcpy(send_buff, receive_buff, receive_num);
            }
            else if (1 == active_send_mode)
            {
                if (active_send_time_count >= active_send_time)
                {
                    active_send_time_count = 0;
                    send_num = active_send_num;
                    memcpy(send_buff, active_send_buff, active_send_num);
                }
                else
                {
                    active_send_time_count++;
                }
            }
            // 数据发送
            if (send_num > 0)
            {
                real_send_num = func_bt_l2_send(sockfd, send_buff, send_num);
                if (real_send_num > 0)
                {
                    printf("[nwrite=%d] ", real_send_num);
                    func_my_print(send_buff, real_send_num, 'c');
                }
                else
                {
                    close(sockfd);
                    connect_state = DISCONNECT;
                }
                memset(send_buff, 0, send_num);
                send_num = 0;
            }
            break;
        }
        usleep(1000); // 1ms
    }
    exit(0);
}
