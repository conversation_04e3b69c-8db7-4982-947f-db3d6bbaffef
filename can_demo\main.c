#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include "mycan.h"

char ver[20] = {"ver01.001"};
#ifndef COMMIT_HASH
#define COMMIT_HASH "unknown"
#endif

char commit[64] = {COMMIT_HASH};

#ifndef REPO_NAME
#define REPO_NAME "unknown"
#endif

char repo_name[64] = {REPO_NAME};

int sock_fd;
struct_can_param can_param = {250000, 0x103, {
                                                 0x100,
                                                 0x00,
                                             },
                              0,
                              0,
                              0,
                              2000000};

unsigned char send_buff[1024], receive_buff[1024];
unsigned int send_num = 0, receive_num = 0;

// 数据收发应用
char active_send_buff[500] = "test can";
int active_send_mode = 0, active_send_num = 8, active_send_time = 1000, active_send_time_count = 0;
int real_send_num = 0;
int loopback_send_mode = 0;

char dev[20];

/*
 * @description : 自定义打印函数
 * @param - buff:  打印数据缓冲区
 * @param - lens:  打印数据长度
 * @param - mode:  打印格式
 * @return		: 无
 */
void func_my_print(unsigned char *buff, unsigned int lens, unsigned char mode)
{
    int i = 0;

    switch (mode)
    {
    case 'H': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("0X%02X  ", buff[i]);
        }
        break;
    case 'h': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("0x%02x  ", buff[i]);
        }
        break;
    case 'd': // 按照10进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%02d  ", buff[i]);
        }
        break;
    case 'c': // 按照字符打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%c", buff[i]);
        }
        break;
    default:
        break;
    }
    printf("\n");
}

/*
 * @description  : 打印参数设置格式
 * @param - pname: 函数名
 * @return		 : 无
 */
static void print_usage(const char *pname)
{
    printf("Usage: %s device [-b baudrate] [-id id_numb] [-filt id mask] [-e] [-fd dbaudrate] [-t data time] [-l] [-L] [-v]"
           "\n\t'-b baudrate' for different baudrate, range 5k to 5M,unit is Kbps"
           "\n\t'-id id_numb' for nonzero can send id"
           "\n\t'-filt id mask' for receive can_id and can_mask"
           "\n\t'-e' for extend id frame"
           "\n\t'-fd dbaudrate' for can fd mode , dbaudrate range 250k to 5M,unit is Kbps"
           "\n\t'-t data time'  for interval set time actively sends the data, unit is s"
           "\n\t'-l' for can loopback mode"
           "\n\t'-L' for app loopback receive data"
           "\n\t'-v' show version"
           "\n\texample : can0 baudrate 250k id 123 filt_id 105 filt_mask 1f0 extend id string data was send every 2s--> ./can_demo can0 -b 250 -id 123 -filt 105 1f0 -e -t abcdef 2"
           "\n\texample : can1 baudrate 250k id 105 filt_id 123 filt_mask 120 extend id --> ./can_demo can1 -b 250 -id 105 -filt 123 1f0"
           "\n\texample : can0 canfd mode baudrate 250k id 123 filt_id 105 filt_mask 1f0 extend id string data was send every 2s--> ./can_demo can0 -b 250 -id 123 -filt 105 1f0 -e -t 12345678 2 -fd 4000"
           "\n\texample : can1 canfd mode baudrate 250k id 105 filt_id 123 filt_mask 120 extend id --> ./can_demo can1 -b 250 -id 105 -filt 123 1f0 -e -fd 4000"
           "\n\texample : can0 canfd mode baudrate 500k extend id dbaudrate 4000k loopback mode 19 data was send every 1s --> ./can_demo can0 -b 500 -filt 0 0 -fd 4000 -t abcdefg 1 -e -l\n ",
           pname);
}

/*
 * @description : 解析函数带入参数
 * @param - numb: 参数个数
 * @param - *param: 带入参数数组指针
 * @param - *canparam: can应用参数
 * @return		: 无
 */
void get_param(int numb, char *param[], struct_can_param *canparam)
{
    int i = 0, len = 0;
    unsigned int baudrate = 0, id = 0, mask = 0;

    if (numb <= 2)
        return;

    for (i = 2; i < numb; i++)
    {
        if (!strcmp(param[i], "-b"))
        {
            i++;
            baudrate = atoi(param[i]);
            switch (baudrate)
            {
            case 5:
            case 10:
            case 20:
            case 40:
            case 50:
            case 80:
            case 100:
            case 125:
            case 200:
            case 250:
            case 400:
            case 500:
            case 666:
            case 800:
            case 1000:
            case 2000:
            case 4000:
            case 5000:
                canparam->baudrate = baudrate * 1000;
                break;
            }
            continue;
        }
        if (!strcmp(param[i], "-id"))
        {
            i++;
            id = strtoul(param[i], NULL, 16);
            if (id)
            {
                canparam->id = (unsigned int)id;
            }
            continue;
        }
        if (!strcmp(param[i], "-filt"))
        {
            i++;
            id = 0;
            id = strtoul(param[i], NULL, 16);
            canparam->filter.can_id = (canid_t)id;
            i++;
            mask = strtoul(param[i], NULL, 16);
            canparam->filter.can_mask = (canid_t)mask;
            continue;
        }
        if (!strcmp(param[i], "-e"))
        {
            canparam->extend = 1;
            continue;
        }
        if (!strcmp(param[i], "-fd"))
        {
            canparam->canfd_mode = CAN_FD_MODE;

            i++;
            baudrate = atoi(param[i]);
            switch (baudrate)
            {
            case 250:
            case 500:
            case 1000:
            case 2000:
            case 4000:
            case 5000:
                canparam->data_baudrate = baudrate * 1000;
                break;
            }
            continue;
        }
        if (!strcmp(param[i], "-t"))
        {
            active_send_mode = 1;

            i++;
            len = strlen(param[i]);
            if (len > 0)
            {
                active_send_num = len;
                memcpy(active_send_buff, param[i], len); // 要发送的字符串

                i++;
                len = atoi(param[i]); // 发送间隔
                if (len > 0)
                {
                    active_send_time = len * 1000; // 转换为ms单位
                    active_send_time_count = active_send_time;
                }
            }
            continue;
        }
        if (!strcmp(param[i], "-l"))
        {
            canparam->loopback_mode = 1;
            continue;
        }
        if (!strcmp(param[i], "-L"))
        {
            loopback_send_mode = 1;
            continue;
        }
        if (!strcmp(param[i], "-v"))
        {
            printf("can_demo ver: %s, repo: %s, commit: %s\n", ver, repo_name, commit);
            continue;
        }
    }
}

/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    int result = 0;
    printf("Copyright 2025 Forlinx Embedded Technology Co., Ltd.\n");
    printf("can_demo ver: %s, repo: %s, commit: %s\n", ver, repo_name, commit);

    // 检测是否有参数
    if (argc < 2 || strncmp(argv[1], "can", 3))
    {
        print_usage(argv[0]);
        exit(1);
    }

    // 检测是否有--h或--help
    if ((!strcmp(argv[1], "--h")) || (!strcmp(argv[1], "--help")))
    {
        print_usage(argv[0]);
        exit(1);
    }

    strcpy(dev, argv[1]);

    // 从main函数带来的参数解析为CAN口参数
    get_param(argc, argv, &can_param);

    // 当知道设备名称时可以直接赋值dev，例strcpy(dev, "can0");
    // 打开CAN口 创建socket 绑定socket
    if (can_param.canfd_mode == CAN_FD_MODE)
    {
        sock_fd = func_open_canfd(dev, can_param);
    }
    else
    {
        sock_fd = func_open_can(dev, can_param);
    }
    if (sock_fd < 0)
    {
        printf("Can't Open deveice %s \n", dev);
        exit(0);
    }
    else
    {
        printf("baudrate = %ldK,can_id = 0x%x,can_filter_id = 0x%x,can_filter_mask = 0x%x,extend id = %d,loopback mode = %d,canfd mode = %d,dbaudrate = %ldK\n",
               can_param.baudrate / 1000, can_param.id, can_param.filter.can_id, can_param.filter.can_mask,
               can_param.extend, can_param.loopback_mode, can_param.canfd_mode, can_param.data_baudrate / 1000);
        // 设置CAN口过滤
        if (can_param.canfd_mode == CAN_FD_MODE)
        {
            result = func_set_canfd(sock_fd, can_param);
        }
        else
        {
            result = func_set_can(sock_fd, can_param);
        }
        if (result < 0)
        {
            perror("set_opt error");
            exit(0);
        }
        // 设置CAN口为非阻塞方式
        fcntl(sock_fd, F_SETFL, O_NONBLOCK); // 非阻塞
    }

    while (1)
    {
        // 接收数据
        if (can_param.canfd_mode == CAN_FD_MODE)
        {
            receive_num = func_receive_canfd_buff(sock_fd, receive_buff, sizeof(receive_buff));
        }
        else
        {
            receive_num = func_receive_can_buff(sock_fd, receive_buff, sizeof(receive_buff));
        }
        if (receive_num > 0)
        {
            printf("[%s nread=%d]  ", dev, receive_num);
            func_my_print(receive_buff, receive_num, 'c'); // 将收到的数据打印出来
        }
        // 组织发送数据
        if ((1 == loopback_send_mode) && (receive_num > 0)) // 数据回环处理
        {
            send_num = receive_num;
            memcpy(send_buff, receive_buff, receive_num);
        }
        else if (1 == active_send_mode)
        {
            if (active_send_time_count >= active_send_time)
            {
                active_send_time_count = 0;
                send_num = active_send_num;
                memcpy(send_buff, active_send_buff, active_send_num);
            }
            else
            {
                active_send_time_count++;
            }
        }

        // 发送数据
        if (send_num > 0)
        {
            if (can_param.canfd_mode == CAN_FD_MODE)
            {
                real_send_num = func_send_canfd_buff(sock_fd, send_buff, send_num, can_param);
            }
            else
            {
                real_send_num = func_send_can_buff(sock_fd, send_buff, send_num, can_param);
            }
            if (real_send_num > 0)
            {
                printf("[%s nwrite=%d] ", dev, real_send_num);
                func_my_print(send_buff, real_send_num, 'c');
            }
            memset(send_buff, 0, send_num);
            send_num = 0;
        }
        usleep(1000); // 1ms
    }
    close(sock_fd);
    exit(0);
}
