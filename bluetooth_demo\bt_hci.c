#include <errno.h>
#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <bluetooth/bluetooth.h>
#include <bluetooth/hci.h>
#include <bluetooth/hci_lib.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <sys/ioctl.h>


/*
 * @description  : 打开本地蓝牙设备
 * @param - *local_addr  : 本地蓝牙设备地址
 * @return		 : 执行结果 无错误返回0 出现错误返回-1
 */
int func_bt_open(bdaddr_t *local_addr)
{
	int value = -1;
	struct hci_dev_info di;
	struct hci_dev_req dr;
	int dev_id, sock;
	char local[18] = {0};

	//创建HCI协议的socket
	if ((sock = socket(AF_BLUETOOTH, SOCK_RAW, BTPROTO_HCI)) < 0)
	{
		perror("Can't open HCI socket.");
		return value;
	}

	//打开设备前先关闭设备，不用管是否关闭成功，因为设备之前打开过需要关闭一下，没打开过关闭无影响。
	ioctl(sock, HCIDEVDOWN, 0);
	//打开蓝牙设备
	if ((ioctl(sock, HCIDEVUP, 0)) < 0)
	{
		perror("dev open failed");
		return value;
	}
	//获取设备ID
	dev_id = hci_get_route(NULL);
	if (dev_id < 0)
	{
		fprintf(stderr, "error code %d: %s\n", errno, strerror(errno));
		return value;
	}

	//获取设备详细信息
	if ((hci_devinfo(dev_id, &di)) < 0)
	{
		perror("get dev info failed");
		return value;
	}
	ba2str(&di.bdaddr, local); //将设备地址转换为字符串并打印
	printf("Local device %s\n", local);
	printf("Local device %s\n", di.name);
	*local_addr = di.bdaddr;

	//设置本地蓝牙可被其他设备扫描到
	dr.dev_id = dev_id;
	dr.dev_opt = SCAN_PAGE | SCAN_INQUIRY;
	if (ioctl(sock, HCISETSCAN, (unsigned long)&dr) < 0)
	{
		perror("dev set scan and inquiry failed");
		return value;
	}

	close(sock);
	value = 0;
	return value;
}

/*
 * @description             : 扫描附近蓝牙设备，通过蓝牙名称获取蓝牙地址
 * @param - *target_name    : 要搜索远端蓝牙设备名称
 * @param - target_name_len : 要搜索远端蓝牙设备名称的长度
 * @param - *target_addr    : 远端蓝牙设备地址
 * @return		            : 执行结果 无错误返回0 出现错误返回-1
 */
int get_addr_from_name(char *target_name, int target_name_len, char *target_addr)
{
	inquiry_info *ii = NULL;
	int max_rsp, num_rsp;
	int dev_id, sock;
	int len, flags, result = -1;
	int i;
	char addr[19] = {0};
	char name[248] = {0};

	/*int hci_get_route(bdaddr_t *bdaddr) 获取可用设备标识号
	带入参数NULL，意味着获取第一个可用蓝牙设备标识号，如果设备只有一个蓝牙，那么这是没问题的
	如果存在多个蓝牙适配器，而第一个（0）被disable了，那么第一个有效设备号是1
	所以，设备有多个蓝牙设备，若想选择"01:23:45:67:89:AB"作为蓝牙适配器的地址，
	用这个函数int dev_id = hci_devid( "01:23:45:67:89:AB" );替代hci_get_route*/

	dev_id = hci_get_route(NULL); //查找蓝牙ID
	if (dev_id < 0)
	{
		fprintf(stderr, "error code %d: %s\n", errno, strerror(errno));
		return result;
	}
	sock = hci_open_dev(dev_id); //打开此蓝牙设备
	if (sock < 0)
	{
		perror("opening socket");
		return result;
	}

	/*在规定的超时时间内搜索附近蓝牙设备
	原函数int hci_inquiry(int dev_id, int len, int max_rsp, const uint8_t *lap, inquiry_info **ii, long flags);
	查询时间最长持续 len*1.28秒
	最大扫描设备数 max_rsp
	查询结果存储在ii中
	flags 设置为IREQ_CACHE_FLUSH 表示在在进行查询操作时会把先前一次查询记录的cache刷新，
	否则flag设置为0的话，即便先前查询的设备已经不处于有效范围内，先前查询的记录也将被返回*/

	len = 8;
	max_rsp = 255;
	flags = IREQ_CACHE_FLUSH;
	ii = (inquiry_info *)malloc(max_rsp * sizeof(inquiry_info));
	num_rsp = hci_inquiry(dev_id, len, max_rsp, NULL, &ii, flags); //搜索附近蓝牙设备
	if (num_rsp < 0)
	{
		perror("hci_inquiry");
	}
	for (i = 0; i < num_rsp; i++)
	{
		ba2str(&(ii + i)->bdaddr, addr); /*蓝牙设备地址bdaddr_t转换为字符串*/
		memset(name, 0, sizeof(name));
		if (hci_read_remote_name(sock, &(ii + i)->bdaddr, sizeof(name), name, 0) < 0)
		{
			strcpy(name, "[unknown]");
		}
		printf("%s %s\n", addr, name);
		if (0 == strncmp(target_name, name, target_name_len))
		{
			strcpy(target_addr, addr);
			result = 0;
		}
	}
	free(ii);
	close(sock);
	return result;
}