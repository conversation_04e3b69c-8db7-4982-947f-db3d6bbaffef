#include <fcntl.h>
#include <unistd.h>
#include <stdio.h>
#include <sys/ioctl.h>
#include <linux/watchdog.h>

char ver[20] = {"ver01.001"};
#ifndef COMMIT_HASH
#define COMMIT_HASH "unknown"
#endif

char commit[64] = {COMMIT_HASH};

#ifndef REPO_NAME
#define REPO_NAME "unknown"
#endif

char repo_name[64] = {REPO_NAME};

int wd_time = 8;
int fd = -1;
// 打开看门狗
int open_watchdog()
{
    int flags;
    fd = open("/dev/watchdog", O_WRONLY);
    if (fd < 0)
    {
        perror("Failed to open /dev/watchdog");
        return 1;
    }
    flags = WDIOS_ENABLECARD;
    ioctl(fd, WDIOC_SETOPTIONS, &flags);
    printf("Watchdog enabled.\n");
    ioctl(fd, WDIOC_SETTIMEOUT, &wd_time);
    close(fd);
    return 0;
}

// 关闭看门狗
int close_watchdog()
{
    int flags;
    fd = open("/dev/watchdog", O_WRONLY);
    if (fd < 0)
    {
        perror("Failed to open /dev/watchdog");
        return 1;
    }
    flags = WDIOS_DISABLECARD;
    ioctl(fd, WDIOC_SETOPTIONS, &flags);
    printf("Watchdog disabled.\n");
    close(fd);
    return 0;
}

int main()
{
    int ret = 0;
    printf("Copyright 2025 Forlinx Embedded Technology Co., Ltd.\n");
    printf("watchdog ver: %s, repo: %s, commit: %s\n", ver, repo_name, commit);
    ret = open_watchdog();
    if (ret != 0)
    {
        printf("open_watchdog failed\n");
        return 1;
    }

    while (1)
    {
        // 喂狗操作
        if (write(fd, "\0", 1) != 1)
        {
            perror("Failed to keep watchdog alive");
            close(fd);
            return 1;
        }
        sleep(5); // 根据超时时间调整间隔
    }

    close(fd);
    return 0;
}
