
COMMIT_HASH := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
REPO_NAME := $(shell basename `git rev-parse --show-toplevel 2>/dev/null` 2>/dev/null || echo "unknown")
CFLAGS += -DCOMMIT_HASH=\"$(COMMIT_HASH)\" -DREPO_NAME=\"$(REPO_NAME)\"

tty_demo:main.o serial.o
	$(CC)	-Wall	main.o serial.o	-o	tty_demo
main.o:main.c serial.h
	$(CC)	-c	-Wall	main.c	-o	main.o
serial.o:serial.c
	$(CC)	-c	-Wall	serial.c	-o	serial.o
clean:
	$(RM) *.o	tty_demo

