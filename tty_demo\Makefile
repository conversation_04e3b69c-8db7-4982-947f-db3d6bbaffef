
# 获取版本信息
COMMIT_HASH := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
REPO_NAME := $(shell basename `git rev-parse --show-toplevel 2>/dev/null` 2>/dev/null || echo "unknown")
BUILD_TIME := $(shell date '+%Y-%m-%d %H:%M:%S' 2>/dev/null || echo "unknown")
GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")

# 编译标志
CFLAGS += -DCOMMIT_HASH=\"$(COMMIT_HASH)\" -DREPO_NAME=\"$(REPO_NAME)\" -DBUILD_TIME=\"$(BUILD_TIME)\" -DGIT_BRANCH=\"$(GIT_BRANCH)\"

# 显示编译信息
.PHONY: show-build-info
show-build-info:
	@echo "=== Build Information ==="
	@echo "Repository: $(REPO_NAME)"
	@echo "Branch: $(GIT_BRANCH)"
	@echo "Commit: $(COMMIT_HASH)"
	@echo "Build Time: $(BUILD_TIME)"
	@echo "========================="

tty_demo: show-build-info main.o serial.o
	@echo "Linking tty_demo..."
	$(CC)	-Wall	main.o serial.o	-o	tty_demo
	@echo "Build completed successfully!"
main.o:main.c serial.h
	$(CC)	-c	-Wall	main.c	-o	main.o
serial.o:serial.c
	$(CC)	-c	-Wall	serial.c	-o	serial.o
clean:
	$(RM) *.o	tty_demo

# 显示版本信息（不编译）
version: show-build-info

# 编译并运行版本检查
test-version: tty_demo
	@echo "Testing version output:"
	@./tty_demo --help 2>/dev/null || echo "Program built successfully"

