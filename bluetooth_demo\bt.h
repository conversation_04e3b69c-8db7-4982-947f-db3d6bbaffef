#ifndef BT_H
#define BT_H

#define CONNECT 1
#define DISCONNECT 0

#define CREATE_TURE 1
#define CREATE_FALSE 0

/*工作模式*/
typedef enum
{
    RFCOMM_SERVER = 0, /*RFCOMM 模式客户端*/
    RFCOMM_CLIENT = 1, /*RFCOMM 模式服务端*/
    L2CAP_SERVER = 2,  /*L2CAP 模式客户端*/
    L2CAP_CLIENT = 3,  /*L2CAP 模式服务端*/
} enum_run_mode;

int func_detect_bt_rf_link(int fd);
int func_create_bt_rf_server_listen(int *listen_fd, unsigned int listen_port, bdaddr_t addr);
int func_bt_rf_server_accept(int listen_fd, int *fd);
int func_bt_rf_client_connect(int *fd, bdaddr_t addr, char *dest_addr, unsigned int listen_port);
int func_bt_rf_receive(int fd, char *p_receive_buff, int count);
int func_bt_rf_send(int fd, char *p_send_buff, int count);
void func_close_bt_rf_server_listen(int listen_fd);
void func_close_bt_rf_link(int fd);

int func_detect_bt_l2_link(int fd);
int func_create_bt_l2_server_listen(int *listen_fd, unsigned int listen_port, bdaddr_t addr);
int func_bt_l2_server_accept(int listen_fd, int *fd);
int func_bt_l2_client_connect(int *fd, bdaddr_t addr, char *dest_addr, unsigned int listen_port);
int func_bt_l2_receive(int fd, char *p_receive_buff, int count);
int func_bt_l2_send(int fd, char *p_send_buff, int count);
void func_close_bt_l2_server_listen(int listen_fd);
void func_close_bt_l2_link(int fd);

#endif