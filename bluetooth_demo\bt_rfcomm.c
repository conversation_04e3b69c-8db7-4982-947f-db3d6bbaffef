#include <errno.h>
#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <net/if.h>
#include <sys/socket.h>
#include <bluetooth/bluetooth.h>
#include <bluetooth/hci.h>
#include <bluetooth/hci_lib.h>
#include <bluetooth/rfcomm.h>
#include <termios.h> /*PPSIX 终端控制定义*/

/*
 * @description  : 检测蓝牙连接状态
 * @param - fd   : 文件描述符
 * @return		 : 执行结果 链接断返回-1 正常返回0
 */
int func_detect_bt_rf_link(int fd)
{
    int info_len = 0, re_err = -1;
    struct rfcomm_conninfo info;

    info_len = sizeof(struct rfcomm_conninfo);
    getsockopt(fd, SOL_RFCOMM, RFCOMM_CONNINFO, (void *)&info, (socklen_t *)&info_len);
    if (info.hci_handle != 0)
    {
        re_err = 0;
    }
    return re_err;
}

/*
 * @description  : 建立蓝牙服务端监听
 * @param - *listen_fd : 监听文件描述符
 * @param - listen_port: 监听端口(1~30)
 * @param - addr : 本地蓝牙地址
 * @return		 : 执行结果 创建失败返回-1 正常返回0
 */
int func_create_bt_rf_server_listen(int *listen_fd, unsigned int listen_port, bdaddr_t addr)
{
    int value = 0, flags = 0;
    struct sockaddr_rc loc_addr = {0};

    //创建套接字
    if ((*listen_fd = socket(AF_BLUETOOTH, SOCK_STREAM, BTPROTO_RFCOMM)) == -1)
    {
        value = -1;
        return value;
    }

    // 设置并绑定本地蓝牙地址
    bzero(&loc_addr, sizeof(loc_addr));
    loc_addr.rc_family = AF_BLUETOOTH;
    loc_addr.rc_bdaddr = addr;
    loc_addr.rc_channel = (uint8_t)listen_port;
    if (bind(*listen_fd, (struct sockaddr *)&loc_addr, sizeof(loc_addr)) < 0)
    {
        perror("\nbind()");
        value = -1;
        return value;
    }

    //设置为非阻塞
    flags = fcntl(*listen_fd, F_GETFL, 0);
    fcntl(*listen_fd, F_SETFL, flags | O_NONBLOCK);
    setsockopt(*listen_fd, SOL_RFCOMM, SO_REUSEADDR, &flags, sizeof(int));

    //监听客户请求
    /*int listen( int sock, int backlog );backlog设置1或2即可，它表示在前一个链接正在连接中，新的连接将put到backlog队列中*/
    if (listen(*listen_fd, 2) < 0)
    {
        printf("listen error%s\n", strerror(errno));
        value = -1;
        return value;
    }

    value = 0;
    return value;
}

/*
 * @description  : 蓝牙服务端与客户端建立链接
 * @param - listen_fd: 监听文件描述符
 * @param - *fd  : 链接文件描述符
 * @return		 : 执行结果 无错误返回0 出现错误返回-1
 */
int func_bt_rf_server_accept(int listen_fd, int *fd)
{
    struct sockaddr_rc rem_addr = {0};
    char buf[19] = {0};
    int value = -1, client = 0, flags = 0;
    socklen_t opt = 0;

    opt = sizeof(rem_addr);
    client = accept(listen_fd, (struct sockaddr *)&rem_addr, &opt);
    if (client > 0)
    {
        ba2str(&rem_addr.rc_bdaddr, buf);
        fprintf(stderr, "accepted connection from %s\n", buf);
        //设置为非阻塞
        flags = fcntl(client, F_GETFL, 0);
        fcntl(client, F_SETFL, flags | O_NONBLOCK);

        *fd = client;
        value = 0;
    }

    return value;
}

/*
 * @description  : 蓝牙客户端与服务端建立链接
 * @param - *fd  : 链接文件描述符
 * @param - addr        : 本地蓝牙地址
 * @param - *dest_addr  : 远端蓝牙地址
 * @param - listen_port : 远端端口
 * @return		 : 执行结果 无错误返回0 出现错误返回-1
 */
int func_bt_rf_client_connect(int *fd, bdaddr_t addr, char *dest_addr, unsigned int listen_port)
{
    struct sockaddr_rc remote_addr = {0}, loc_addr = {0};
    int value = -1, flags = 0;

    //创建套接字
    if ((*fd = socket(AF_BLUETOOTH, SOCK_STREAM, BTPROTO_RFCOMM)) == -1)
    {
        value = -1;
        return value;
    }

    // 设置并绑定本地蓝牙地址
    bzero(&loc_addr, sizeof(loc_addr));
    loc_addr.rc_family = AF_BLUETOOTH;
    loc_addr.rc_bdaddr = addr;
    loc_addr.rc_channel = (uint8_t)listen_port;
    if (bind(*fd, (struct sockaddr *)&loc_addr, sizeof(loc_addr)) < 0)
    {
        perror("\nbind()");
        value = -1;
        return value;
    }

    //设置链接服务器地址结构
    bzero(&remote_addr, sizeof(remote_addr));
    remote_addr.rc_family = AF_BLUETOOTH;
    remote_addr.rc_channel = (uint8_t)listen_port;
    str2ba(dest_addr, &remote_addr.rc_bdaddr);

    //尝试建立连接
    if (connect(*fd, (struct sockaddr *)&remote_addr, sizeof(remote_addr)) < 0)
    {
        perror("connect");
        value = -1;
        return value;
    }

    //设置为非阻塞
    flags = fcntl(*fd, F_GETFL, 0);
    fcntl(*fd, F_SETFL, flags | O_NONBLOCK);

    value = 0;
    return value;
}
/*
 * @description  : 蓝牙接收数据函数
 * @param - fd   : 链接文件描述符
 * @param - *p_receive_buff: 接收数据缓冲区首地址
 * @param - count: 最大接收长度
 * @return		 : 实际接收数据长度
 */
int func_bt_rf_receive(int fd, char *p_receive_buff, int count)
{
    int receive_numb = 0;

    memset(p_receive_buff, 0x0, count);
    receive_numb = read(fd, p_receive_buff, count);

    return receive_numb;
}

/*
 * @description  : 蓝牙发送数据函数
 * @param - fd   : 链接文件描述符
 * @param - *p_send_buff: 发送数据缓冲区首地址
 * @param - count: 发送数据长度
 * @return		 : 实际发送数据长度
 */
int func_bt_rf_send(int fd, char *p_send_buff, int count)
{
    int send_numb = 0;

    send_numb = write(fd, p_send_buff, count);
    if (count != send_numb)
    {
        printf("terminated prematurely write err\n");
        return -1;
    }
    return send_numb;
}

/*
 * @description  : 蓝牙服务端关闭监听
 * @param - fd   : 监听文件描述符
 * @return		 : 无
 */
void func_close_bt_rf_server_listen(int listen_fd)
{
    if (listen_fd > 0)
    {
        close(listen_fd);
    }
}

/*
 * @description  : 蓝牙关闭链接
 * @param - fd   : 链接文件描述符
 * @return		 : 无
 */
void func_close_bt_rf_link(int fd)
{
    if (fd > 0)
    {
        close(fd);
    }
}