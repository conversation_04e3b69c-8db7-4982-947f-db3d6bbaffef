MAKE = make

# 平台选择变量
PLATFORM ?= ALL

# 定义不同平台对应的目录
FCU1501_DIRS := bluetooth_demo can_demo net_demo tty_demo wd_demo
OK113_DIRS := can_demo net_demo tty_demo wd_demo  
OK3568_DIRS := bluetooth_demo can_demo net_demo tty_demo wd_demo
ALL_DIRS := bluetooth_demo can_demo net_demo tty_demo wd_demo

# 根据平台选择编译目录
ifeq ($(PLATFORM), FCU1501)
    SUBDIRS := $(FCU1501_DIRS)
else ifeq ($(PLATFORM), OK113)
    SUBDIRS := $(OK113_DIRS)
else ifeq ($(PLATFORM), OK3568)
    SUBDIRS := $(OK3568_DIRS)
else
    SUBDIRS := $(ALL_DIRS)
endif

all:
	for dir in $(SUBDIRS); do \
		$(MAKE) -C $$dir ;\
		if [ "$$?" != "0" ]; then\
			echo "compile $$dir fail"; \
			exit 1 ; \
		fi;\
	done

ECHO:
	@echo begin compile $(SUBDIRS)

clean: $(clean_dirs) 
	for dir in $(SUBDIRS);\
	do $(MAKE) -C $$dir clean;\
	done
	
cleanall:
	for dir in $(SUBDIRS);\
	do $(MAKE) -C $$dir cleanall;\
	done

help:
	@echo "Usage: make [PLATFORM=<platform>] [target]"
	@echo ""
	@echo "Supported platforms:"
	@echo "  FCU1501  - Compile: $(FCU1501_DIRS)"
	@echo "  OK113    - Compile: $(OK113_DIRS)"
	@echo "  OK3568   - Compile: $(OK3568_DIRS)"
	@echo "  ALL      - Compile: $(ALL_DIRS) (default)"
	@echo ""
	@echo "Examples:"
	@echo "  make PLATFORM=FCU1501 all"
	@echo "  make PLATFORM=OK113 clean"
	@echo "  make help"